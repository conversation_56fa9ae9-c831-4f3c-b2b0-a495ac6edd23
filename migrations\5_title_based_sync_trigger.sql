-- File: migrations/5_title_based_sync_trigger.sql
-- Description: 基于标题的同步触发器
-- 
-- 同步规则：
-- 1. 如果 is_parsed = false 且 title 包含 '待解析'，则不同步
-- 2. 其他所有情况都正常同步

-- 创建新的触发器函数
CREATE OR REPLACE FUNCTION log_resource_change_title_based() RETURNS TRIGGER AS $$
BEGIN
  -- 删除操作：无条件记录同步任务
  IF (TG_OP = 'DELETE') THEN
    INSERT INTO meilisearch_sync_log (resource_key, operation)
    VALUES (OLD.resource_key, TG_OP);
    RETURN OLD;
    
  -- 插入和更新操作
  ELSE
    -- 检查是否为不需要同步的情况：is_parsed = false 且 title 包含 '待解析'
    IF (NEW.is_parsed = false AND NEW.title IS NOT NULL AND NEW.title LIKE '%待解析%') THEN
      -- 不同步，直接返回
      RETURN NEW;
    ELSE
      -- 其他所有情况都同步
      INSERT INTO meilisearch_sync_log (resource_key, operation)
      VALUES (NEW.resource_key, TG_OP);
      RETURN NEW;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 更新触发器使用新函数
DROP TRIGGER IF EXISTS pan_resources_log_trigger ON pan_resources;
CREATE TRIGGER pan_resources_log_trigger
AFTER INSERT OR UPDATE OR DELETE ON pan_resources
FOR EACH ROW EXECUTE FUNCTION log_resource_change_title_based();

-- 添加注释
COMMENT ON FUNCTION log_resource_change_title_based() 
IS '基于标题的资源变更日志函数：未解析且标题包含"待解析"的资源不同步';

COMMENT ON TRIGGER pan_resources_log_trigger ON pan_resources
IS '基于标题的同步触发器：过滤掉未解析且标题包含"待解析"的资源';

-- 测试函数
CREATE OR REPLACE FUNCTION test_title_based_trigger() RETURNS TABLE(
    test_case TEXT,
    expected_sync BOOLEAN,
    actual_sync_count INTEGER,
    result TEXT
) AS $$
DECLARE
    test_resource_key VARCHAR(50);
    sync_count INTEGER;
BEGIN
    -- 清理测试数据
    DELETE FROM meilisearch_sync_log WHERE resource_key LIKE 'test_%';
    DELETE FROM pan_resources WHERE resource_key LIKE 'test_%';
    
    -- 测试1：未解析且标题包含"待解析"（不应该同步）
    test_resource_key := 'test_pending_' || extract(epoch from now())::text;
    INSERT INTO pan_resources (resource_key, pan_type, original_url, title, is_parsed)
    VALUES (test_resource_key, 2, 'https://test.com', '待解析资源: 测试文件', false);
    
    SELECT COUNT(*) INTO sync_count FROM meilisearch_sync_log WHERE resource_key = test_resource_key;
    RETURN QUERY SELECT 
        '未解析且标题包含"待解析"'::TEXT,
        false::BOOLEAN,
        sync_count,
        CASE WHEN sync_count = 0 THEN '通过' ELSE '失败' END::TEXT;
    
    -- 测试2：未解析但标题不包含"待解析"（应该同步）
    test_resource_key := 'test_normal_unparsed_' || extract(epoch from now())::text;
    INSERT INTO pan_resources (resource_key, pan_type, original_url, title, is_parsed)
    VALUES (test_resource_key, 2, 'https://test.com', '正常的未解析资源', false);
    
    SELECT COUNT(*) INTO sync_count FROM meilisearch_sync_log WHERE resource_key = test_resource_key;
    RETURN QUERY SELECT 
        '未解析但标题正常'::TEXT,
        true::BOOLEAN,
        sync_count,
        CASE WHEN sync_count = 1 THEN '通过' ELSE '失败' END::TEXT;
    
    -- 测试3：已解析资源（应该同步）
    test_resource_key := 'test_parsed_' || extract(epoch from now())::text;
    INSERT INTO pan_resources (resource_key, pan_type, original_url, title, is_parsed)
    VALUES (test_resource_key, 2, 'https://test.com', '已解析的资源', true);
    
    SELECT COUNT(*) INTO sync_count FROM meilisearch_sync_log WHERE resource_key = test_resource_key;
    RETURN QUERY SELECT 
        '已解析资源'::TEXT,
        true::BOOLEAN,
        sync_count,
        CASE WHEN sync_count = 1 THEN '通过' ELSE '失败' END::TEXT;
    
    -- 测试4：已解析但标题包含"待解析"（应该同步，因为已解析）
    test_resource_key := 'test_parsed_with_pending_title_' || extract(epoch from now())::text;
    INSERT INTO pan_resources (resource_key, pan_type, original_url, title, is_parsed)
    VALUES (test_resource_key, 2, 'https://test.com', '待解析资源: 但已解析完成', true);
    
    SELECT COUNT(*) INTO sync_count FROM meilisearch_sync_log WHERE resource_key = test_resource_key;
    RETURN QUERY SELECT 
        '已解析但标题包含"待解析"'::TEXT,
        true::BOOLEAN,
        sync_count,
        CASE WHEN sync_count = 1 THEN '通过' ELSE '失败' END::TEXT;
    
    -- 清理测试数据
    DELETE FROM meilisearch_sync_log WHERE resource_key LIKE 'test_%';
    DELETE FROM pan_resources WHERE resource_key LIKE 'test_%';
END;
$$ LANGUAGE plpgsql;

-- 使用说明
/*
新的触发器逻辑：

1. DELETE操作：无条件同步
2. INSERT/UPDATE操作：
   - 如果 is_parsed = false 且 title 包含 '待解析'：不同步
   - 其他所有情况：正常同步

测试命令：
SELECT * FROM test_title_based_trigger();
*/